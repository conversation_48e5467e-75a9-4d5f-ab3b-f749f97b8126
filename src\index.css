@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;
:root {
  --background: #f9f9f9;
  --foreground: #333333;
  --card: #ffffff;
  --card-foreground: #333333;
  --popover: #ffffff;
  --popover-foreground: #333333;
  --primary: #6c5ce7;
  --primary-foreground: #ffffff;
  --secondary: #a1c9f2;
  --secondary-foreground: #333333;
  --muted: #c9c4b5;
  --muted-foreground: #6e6e6e;
  --accent: #8b9467;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #d4d4d4;
  --input: #d4d4d4;
  --ring: #6c5ce7;
  --chart-1: #6c5ce7;
  --chart-2: #8e44ad;
  --chart-3: #4b0082;
  --chart-4: #6495ed;
  --chart-5: #4682b4;
  --sidebar: #f9f9f9;
  --sidebar-foreground: #333333;
  --sidebar-primary: #6c5ce7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #8b9467;
  --sidebar-accent-foreground: #ffffff;
  --sidebar-border: #d4d4d4;
  --sidebar-ring: #6c5ce7;
  --font-sans: 'Inter', 'Roboto', 'Segoe UI', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: 'Georgia', 'Times New Roman', serif;
  --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10);
  --shadow: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 2px 4px -3px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 4px 6px -3px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 8px 10px -3px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: #1a1d23;
  --foreground: #e5e5e5;
  --card: #2f3436;
  --card-foreground: #e5e5e5;
  --popover: #2f3436;
  --popover-foreground: #e5e5e5;
  --primary: #6c5ce7;
  --primary-foreground: #ffffff;
  --secondary: #4b0082;
  --secondary-foreground: #e5e5e5;
  --muted: #444444;
  --muted-foreground: #a3a3a3;
  --accent: #6495ed;
  --accent-foreground: #e5e5e5;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #444444;
  --input: #444444;
  --ring: #6c5ce7;
  --chart-1: #6c5ce7;
  --chart-2: #8e44ad;
  --chart-3: #4b0082;
  --chart-4: #6495ed;
  --chart-5: #4682b4;
  --sidebar: #1a1d23;
  --sidebar-foreground: #e5e5e5;
  --sidebar-primary: #6c5ce7;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #6495ed;
  --sidebar-accent-foreground: #e5e5e5;
  --sidebar-border: #444444;
  --sidebar-ring: #6c5ce7;
  --font-sans: 'Inter', 'Roboto', 'Segoe UI', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  --font-serif: 'Georgia', 'Times New Roman', serif;
  --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
  --radius: 0.5rem;
  --shadow-2xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0px 5px 10px -2px hsl(0 0% 0% / 0.05);
  --shadow-sm: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10);
  --shadow: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 1px 2px -3px hsl(0 0% 0% / 0.10);
  --shadow-md: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 2px 4px -3px hsl(0 0% 0% / 0.10);
  --shadow-lg: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 4px 6px -3px hsl(0 0% 0% / 0.10);
  --shadow-xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.10), 0px 8px 10px -3px hsl(0 0% 0% / 0.10);
  --shadow-2xl: 0px 5px 10px -2px hsl(0 0% 0% / 0.25);
}



/* Animated scrolling categories */
@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-right {
  0% {
    transform: translateX(-50%);
  }
  100% {
    transform: translateX(0);
  }
}

.animate-scroll-left {
  animation: scroll-left 25s linear infinite;
}

.animate-scroll-right {
  animation: scroll-right 25s linear infinite;
}

.animate-scroll-left:hover,
.animate-scroll-right:hover {
  animation-play-state: paused;
}

/* Custom gradient backgrounds */
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

/* Enhanced container spacing */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

/* Responsive padding adjustments */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

@media (min-width: 769px) {
  .container {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

/* Azerbaijani character support */
body, html {
  font-family: 'Inter', 'Roboto', 'Segoe UI', 'Helvetica Neue', 'Arial', 'Noto Sans', sans-serif;
  font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Specific styling for Azerbaijani characters */
.az-text {
  font-family: 'Inter', 'Roboto', 'Segoe UI', system-ui, sans-serif;
  font-variant-ligatures: common-ligatures;
  text-rendering: optimizeLegibility;
}

/* Ensure proper rendering of ə character */
h1, h2, h3, h4, h5, h6, p, span, div {
  font-family: 'Inter', 'Roboto', 'Segoe UI', 'Helvetica Neue', 'Arial', sans-serif;
  font-feature-settings: "kern" 1;
}

/* Fix for gradient text with Azerbaijani characters */
.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
  font-family: 'Inter', 'Roboto', 'Segoe UI', sans-serif;
}

/* Category card hover effects */
.category-card {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 0.5px solid #e5e7eb !important;
}

.category-card:hover {
  border: 0.5px solid #9ca3af !important;
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

/* Special hover effect for the center card */
.category-card.center-card {
  border: 2px solid #374151 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.category-card.center-card:hover {
  border: 2px solid #1f2937 !important;
  box-shadow: 0 20px 40px -4px rgba(0, 0, 0, 0.15), 0 8px 16px -4px rgba(0, 0, 0, 0.1);
  transform: translateY(-4px) scale(1.02);
}

/* Thin border utilities */
.border-thin {
  border-width: 0.5px;
}

.border-gray-light {
  border-color: #f3f4f6;
}

.hover\:border-gray-medium:hover {
  border-color: #d1d5db;
}