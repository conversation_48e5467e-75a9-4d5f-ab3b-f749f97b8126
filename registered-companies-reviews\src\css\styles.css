body {
    font-family: '<PERSON><PERSON>', sans-serif;
    background: #fafbfc;
    margin: 0;
    padding: 20px;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

.header {
    text-align: center;
    margin-bottom: 40px;
}

.header h1 {
    font-size: 2rem;
    color: #515bc3;
}

.review-summary {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;
}

.review-summary h2 {
    font-size: 1.5rem;
    margin-bottom: 12px;
}

.review-history {
    display: flex;
    justify-content: space-between;
    margin-bottom: 24px;
}

.review-history select {
    padding: 8px;
    border-radius: 4px;
    border: 1px solid #ccc;
}

.review-list {
    list-style: none;
    padding: 0;
}

.review-list li {
    background: #f9f9f9;
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 10px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.review-list li h3 {
    margin: 0 0 4px;
    font-size: 1.2rem;
}

.review-list li p {
    margin: 4px 0;
    color: #555;
}

.footer {
    text-align: center;
    margin-top: 40px;
    color: #888;
}