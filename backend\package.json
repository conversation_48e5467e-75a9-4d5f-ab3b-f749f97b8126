{"name": "grumble-backend", "version": "1.0.0", "description": "Backend API for Grumble complaint management system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "seed": "node scripts/seedData.js"}, "keywords": ["complaints", "api", "nodejs", "express"], "author": "Grumble Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "compression": "^1.7.4", "passport": "^0.6.0", "passport-google-oauth20": "^2.0.0", "passport-facebook": "^3.0.0", "express-session": "^1.17.3", "crypto": "^1.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3"}}