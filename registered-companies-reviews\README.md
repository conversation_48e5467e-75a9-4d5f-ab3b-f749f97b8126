# Registered Companies Reviews

This project provides a web page for registered companies to view their reviews. The page includes features such as displaying the number of accepted reviews, review history over different time frames (daily, weekly, monthly, yearly), and the overall score based on the reviews received.

## Project Structure

```
registered-companies-reviews
├── src
│   ├── index.html          # Main HTML page for viewing reviews
│   ├── css
│   │   └── styles.css      # Styles for the HTML page
│   ├── js
│   │   └── main.js         # JavaScript functionality for the page
│   └── assets              # Directory for additional assets (images, icons, etc.)
└── README.md               # Documentation for the project
```

## Features

- **Accepted Reviews Count**: Displays the total number of reviews that have been accepted.
- **Review History**: Allows users to view their review history based on selected time frames:
  - Daily
  - Weekly
  - Monthly
  - Yearly
- **Overall Score**: Shows the overall score calculated from the reviews.

## Setup Instructions

1. Clone the repository to your local machine.
2. Open the `src/index.html` file in a web browser to view the registered companies reviews page.
3. Ensure that the `src/css/styles.css` and `src/js/main.js` files are linked correctly in the HTML file for proper styling and functionality.

## Usage

- Navigate through the review history by selecting the desired time frame.
- View the total number of accepted reviews and the overall score based on the reviews received.

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any enhancements or bug fixes.