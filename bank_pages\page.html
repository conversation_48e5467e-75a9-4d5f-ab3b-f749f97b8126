<!DOCTYPE html>
<html lang="az-az" class="fa-events-icons-ready">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="author" content="Grumble">
    <meta name="msapplication-TileColor" content="#515bc3">
    <meta name="theme-color" content="#515bc3">
    <link rel="shortcut icon" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/svgs/solid/building.svg" type="image/svg+xml">
    <link rel="apple-touch-icon" sizes="180x180" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/svgs/solid/building.svg">
    <link rel="icon" type="image/png" sizes="32x32" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/svgs/solid/building.svg">
    <link rel="icon" type="image/png" sizes="16x16" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/svgs/solid/building.svg">
    <link rel="manifest" href="#">
    <link rel="mask-icon" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.5.2/svgs/solid/building.svg" color="#515bc3">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-ENjdO4Dr2bkBIFxQpeoA6VKHr8BLZ9g1l6lZ9g5lZ9g5lZ9g5lZ9g5lZ9g5lZ9g5" crossorigin="anonymous">
    <link rel="preconnect" href="https://fonts.gstatic.com/">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;700&display=swap" rel="stylesheet">
    <link rel="canonical" href="#">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/css/header-new.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/css/footer-new.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/css/custom.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/css/for-companies.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/css/errorlist.css">
    <title>Grumble - Şirkət Paneli</title>
    <meta name="description" content="Şirkət paneli - müştəri rəyləri və şikayətləri idarə edin">
    <meta property="og:title" content="Grumble - Şirkət Paneli">
    <meta property="og:description" content="Şirkət paneli - müştəri rəyləri və şikayətləri idarə edin">
    <meta property="og:image" content="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/img/logoSVG.png">
    <meta property="og:url" content="#">
    <meta property="og:site_name" content="Grumble">
    <link rel="alternate" href="#" hreflang="x-default">
    <link rel="alternate" href="#" hreflang="az-az">
    <link rel="alternate" href="#" hreflang="ru-ru">
  <style>
    body {
      font-family: 'Roboto', sans-serif;
      background: #fafbfc;
      margin: 0;
      padding: 0;
    }

    /* Header Styles */
    #header {
      background: #fff;
      border-bottom: 1px solid #eee;
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .header_wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
    }

    .menu_wrapper {
      display: flex;
      align-items: center;
      gap: 32px;
    }

    .logo {
      display: flex;
      align-items: center;
      gap: 8px;
      text-decoration: none;
      color: #515bc3;
      font-weight: bold;
      font-size: 1.2rem;
    }

    .logo img {
      width: 32px;
      height: 32px;
    }

    .menu_links {
      display: flex;
      list-style: none;
      margin: 0;
      padding: 0;
      gap: 24px;
    }

    .menu_links .link a {
      text-decoration: none;
      color: #333;
      font-weight: 500;
      transition: color 0.2s;
    }

    .menu_links .link a:hover {
      color: #515bc3;
    }

    .search_form_wrapper {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .search_form {
      position: relative;
      display: flex;
      align-items: center;
    }

    .search_form input {
      width: 300px;
      padding: 8px 40px 8px 12px;
      border: 1px solid #ddd;
      border-radius: 6px;
      font-size: 14px;
    }

    .btn_search {
      position: absolute;
      right: 8px;
      background: none;
      border: none;
      cursor: pointer;
    }

    .btn_search img {
      width: 16px;
      height: 16px;
    }

    .write-review-btn {
      background: #515bc3;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .write-review-btn svg {
      width: 16px;
      height: 16px;
      fill: currentColor;
    }

    .login_wrapper {
      display: flex;
      align-items: center;
      gap: 16px;
    }

    .user_wrapper {
      display: flex;
      align-items: center;
      gap: 16px;
      list-style: none;
      margin: 0;
      padding: 0;
    }

    .user-profile .btn_toggle {
      display: flex;
      align-items: center;
      gap: 8px;
      background: none;
      border: none;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 6px;
      transition: background-color 0.2s;
    }

    .user-profile .btn_toggle:hover {
      background-color: #f5f5f5;
    }

    .user-profile .dp img {
      width: 32px;
      height: 32px;
      border-radius: 50%;
    }

    .language_selection {
      position: relative;
    }

    .language_selection .title {
      background: none;
      border: 1px solid #ddd;
      padding: 6px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
    }

    .lang_body {
      position: absolute;
      top: 100%;
      right: 0;
      background: white;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      display: none;
      min-width: 60px;
    }

    .language_selection:hover .lang_body {
      display: block;
    }

    .lang_body a {
      display: block;
      padding: 8px 12px;
      text-decoration: none;
      color: #333;
      font-size: 14px;
    }

    .lang_body a:hover {
      background-color: #f5f5f5;
    }

    .ham-menu {
      display: none;
      background: none;
      border: none;
      cursor: pointer;
    }

    /* Main Content Styles */
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 16px;
    }

    .breadcrumb {
      margin: 24px 0;
    }

    .breadcrumb ul {
      list-style: none;
      padding: 0;
      display: flex;
      gap: 8px;
      align-items: center;
    }

    .breadcrumb a {
      color: #515bc3;
      text-decoration: none;
    }

    .breadcrumb .current-page {
      color: #666;
    }

    .welcome-section {
      background: linear-gradient(135deg, #515bc3, #6366f1);
      color: white;
      padding: 32px;
      border-radius: 12px;
      margin-bottom: 32px;
      text-align: center;
    }

    .welcome-section h1 {
      font-size: 2rem;
      margin-bottom: 8px;
      font-weight: 700;
    }

    .welcome-section p {
      font-size: 1.1rem;
      opacity: 0.9;
      margin-bottom: 24px;
    }

    .logout-btn {
      background: rgba(255,255,255,0.2);
      color: white;
      border: 1px solid rgba(255,255,255,0.3);
      padding: 8px 16px;
      border-radius: 6px;
      text-decoration: none;
      display: inline-block;
      transition: all 0.2s;
    }

    .logout-btn:hover {
      background: rgba(255,255,255,0.3);
      color: white;
    }

    .company-list a.company-wrapper {
      display: block;
      margin-bottom: 16px;
      text-decoration: none;
      color: inherit;
      transition: transform 0.2s;
    }

    .company-list a.company-wrapper:hover {
      transform: translateY(-2px);
    }

    .company {
      display: flex;
      align-items: center;
      padding: 20px;
      background: #fff;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      border: 1px solid #f0f0f0;
    }

    .company-logo {
      margin-right: 16px;
    }

    .company-logo img {
      width: 64px;
      height: 64px;
      object-fit: contain;
      border-radius: 8px;
      background: #f8f9fa;
      padding: 8px;
    }

    .company-info {
      flex: 1;
    }

    .company-name h5 {
      margin: 0 0 8px 0;
      font-size: 1.2rem;
      font-weight: 600;
      color: #333;
    }

    .stars {
      display: flex;
      align-items: center;
      gap: 2px;
      margin-bottom: 8px;
    }

    .stars .star {
      width: 16px;
      height: 16px;
      color: #ffc107;
    }

    .stars .star.empty {
      color: #e9ecef;
    }

    .star-rating__result {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
    }

    .star-rating__result span {
      font-weight: bold;
      color: #515bc3;
      font-size: 1.1rem;
    }

    .company-title {
      font-size: 0.9rem;
      color: #666;
      margin-bottom: 4px;
    }

    .company-stats {
      font-size: 0.9rem;
      color: #888;
    }

    .categories {
      background: #fff;
      border-radius: 12px;
      padding: 20px;
      margin-bottom: 24px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      border: 1px solid #f0f0f0;
    }

    .categories h5 {
      font-size: 1.1rem;
      margin-bottom: 16px;
      font-weight: 600;
      color: #333;
    }

    .categories ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }

    .categories .result {
      margin-bottom: 8px;
      cursor: pointer;
      padding: 8px 12px;
      border-radius: 6px;
      transition: background-color 0.2s;
      color: #555;
    }

    .categories .result:hover {
      background-color: #f8f9fa;
      color: #515bc3;
    }

    .categories .result.active {
      background-color: #515bc3;
      color: white;
    }

    .filter-sort {
      margin-bottom: 24px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .filter-sort h1 {
      font-size: 1.8rem;
      margin: 0;
      color: #333;
      font-weight: 700;
    }

    .sort {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .sort span {
      color: #666;
      font-weight: 500;
    }

    .form-select {
      border: 1px solid #ddd;
      border-radius: 6px;
      padding: 6px 12px;
      font-size: 14px;
    }

    .pagination-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      margin-top: 32px;
    }

    .page-numbers {
      display: flex;
      gap: 4px;
    }

    .page-numbers .page {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border-radius: 6px;
      background: #f8f9fa;
      color: #515bc3;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.2s;
    }

    .page-numbers .page:hover {
      background: #e9ecef;
    }

    .page.active {
      background: #515bc3;
      color: #fff;
    }

    .companies-brandss {
      margin: 48px 0;
      text-align: center;
      background: #fff;
      padding: 40px;
      border-radius: 12px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.06);
      border: 1px solid #f0f0f0;
    }

    .companies-brandss h3 {
      font-size: 1.5rem;
      margin-bottom: 12px;
      color: #333;
      font-weight: 600;
    }

    .companies-brandss p {
      color: #666;
      margin-bottom: 24px;
      font-size: 1.1rem;
    }

    .btn-primary {
      background: #515bc3 !important;
      border: none !important;
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 600;
      transition: all 0.2s;
    }

    .btn-primary:hover {
      background: #4147a3 !important;
      transform: translateY(-1px);
    }

    /* Modal Styles */
    .modal-content {
      border-radius: 12px;
      border: none;
      box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    }

    .modal-header {
      border-bottom: 1px solid #f0f0f0;
      padding: 20px 24px;
    }

    .modal-title {
      font-weight: 600;
      color: #333;
    }

    .modal-body {
      padding: 24px;
    }

    .company_sign_up_pop_up_input {
      width: 100%;
      padding: 12px;
      border-radius: 8px;
      border: 1px solid #ddd;
      font-size: 14px;
      transition: border-color 0.2s;
    }

    .company_sign_up_pop_up_input:focus {
      outline: none;
      border-color: #515bc3;
      box-shadow: 0 0 0 3px rgba(81, 91, 195, 0.1);
    }

    .company_sign_up_pop_up_submit {
      width: 100%;
      background: #515bc3;
      color: #fff;
      border: none;
      border-radius: 8px;
      padding: 12px;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.2s;
    }

    .company_sign_up_pop_up_submit:hover {
      background: #4147a3;
      transform: translateY(-1px);
    }

    /* Footer Styles */
    #footer {
      background: #fff;
      border-top: 1px solid #eee;
      margin-top: 64px;
    }

    .footer_menu_wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32px 0;
    }

    .footer_menu {
      display: flex;
      gap: 48px;
    }

    .ft_links {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .ft_link {
      color: #666;
      text-decoration: none;
      font-size: 14px;
      transition: color 0.2s;
    }

    .ft_link:hover {
      color: #515bc3;
    }

    .social_links {
      display: flex;
      gap: 16px;
    }

    .social_links a {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      background: #515bc3;
      color: white;
      border-radius: 50%;
      text-decoration: none;
      transition: all 0.2s;
    }

    .social_links a:hover {
      background: #4147a3;
      transform: translateY(-2px);
    }

    .footer_bottom {
      border-top: 1px solid #f0f0f0;
      padding: 20px 0;
    }

    .f_wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .f_logo img {
      height: 32px;
    }

    .f_copyright {
      color: #666;
      font-size: 14px;
      margin: 0;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .header_wrapper {
        flex-wrap: wrap;
        gap: 16px;
      }

      .search_form input {
        width: 200px;
      }

      .menu_links {
        display: none;
      }

      .ham-menu {
        display: block;
      }

      .company {
        flex-direction: column;
        align-items: flex-start;
        text-align: left;
      }

      .company-logo {
        margin-right: 0;
        margin-bottom: 16px;
      }

      .filter-sort {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
      }

      .footer_menu {
        flex-direction: column;
        gap: 24px;
      }

      .f_wrapper {
        flex-direction: column;
        gap: 16px;
        text-align: center;
      }
    }

    @media (max-width: 576px) {
      .container {
        padding: 0 12px;
      }

      .welcome-section {
        padding: 24px 16px;
      }

      .welcome-section h1 {
        font-size: 1.5rem;
      }

      .categories, .companies-brandss {
        padding: 16px;
      }

      .company {
        padding: 16px;
      }
    }
  </style>
</head>
<body>
<header id="header">
    <div class="container">
        <div class="header_wrapper">
            <div class="menu_wrapper">
                <a href="#" class="logo">
                    <img src="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/img/grumble-logo.svg" alt="Grumble logo">
                </a>
                <ul class="menu_links">
                    <li class="link"><a href="#">Rəylər</a></li>
                    <li class="link"><a href="#">Şirkətlər</a></li>
                </ul>
            </div>
            <div class="search_form_wrapper">
                <form action="#" id="search_form" class="search_form">
                    <input id="search-input" type="text" autocomplete="off" name="q" value="" placeholder="Kateqoriya və ya şirkət axtar">
                    <button type="submit" class="btn_search">
                        <img class="v-desktop" src="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/img/loupe.svg" alt="loupe.svg">
                    </button>
                </form>
                <button id="write-review-btn" class="write-review-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">
                        <path d="M441 58.9L453.1 71c9.4 9.4 9.4 24.6 0 33.9L424 134.1 377.9 88 407 58.9c9.4-9.4 24.6-9.4 33.9 0zM209.8 256.2L344 121.9 390.1 168 255.8 302.2c-2.9 2.9-6.5 5-10.4 6.1l-58.5 16.7 16.7-58.5c1.1-3.9 3.2-7.5 6.1-10.4zM373.1 25L175.8 222.2c-8.7 8.7-15 19.4-18.3 31.1l-28.6 100c-2.4 8.4-.1 17.4 6.1 23.6s15.2 8.5 23.6 6.1l100-28.6c11.8-3.4 22.5-9.7 31.1-18.3L487 138.9c28.1-28.1 28.1-73.7 0-101.8L474.9 25C446.8-3.1 401.2-3.1 373.1 25zM88 64C39.4 64 0 103.4 0 152L0 424c0 48.6 39.4 88 88 88l272 0c48.6 0 88-39.4 88-88l0-112c0-13.3-10.7-24-24-24s-24 10.7-24 24l0 112c0 22.1-17.9 40-40 40L88 464c-22.1 0-40-17.9-40-40l0-272c0-22.1 17.9-40 40-40l112 0c13.3 0 24-10.7 24-24s-10.7-24-24-24L88 64z"></path>
                    </svg>
                </button>
            </div>
            <div class="login_wrapper">
                <ul class="user_wrapper">
                    <li class="user-profile">
                        <button class="btn_toggle user" id="userProfileBtn">
                            <div class="dp">
                                <img src="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/img/user-profile-img-placeholder-light.svg" alt="user-profile">
                            </div>
                            <p id="companyNameDisplay">Kapital Bank</p>
                        </button>
                        <div class="dropdown" id="userDropdown" style="display: none; position: absolute; top: 100%; right: 0; background: white; border: 1px solid #ddd; border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); min-width: 180px; z-index: 1000;">
                            <a href="#" style="display: block; padding: 12px 16px; text-decoration: none; color: #333; border-bottom: 1px solid #f0f0f0;">Profil</a>
                            <a href="#" style="display: block; padding: 12px 16px; text-decoration: none; color: #333; border-bottom: 1px solid #f0f0f0;">Parametrlər</a>
                            <a href="#" id="logoutBtn" style="display: block; padding: 12px 16px; text-decoration: none; color: #dc3545;">Çıxış</a>
                        </div>
                    </li>
                    <li class="write_review">
                        <a href="#" class="btn_write_review" style="background: #515bc3; color: white; padding: 8px 16px; border-radius: 6px; text-decoration: none;">Rəy Bildir</a>
                    </li>
                </ul>
                <div class="language_selection">
                    <button class="title">AZ</button>
                    <div class="lang_body">
                        <a href="#">AZ</a>
                        <a href="#">RU</a>
                    </div>
                </div>
            </div>
            <button class="ham-menu">
                <img src="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/img/open-menu.svg" alt="open-menu.svg">
            </button>
        </div>
    </div>
</header>
<main>
    <div class="container">
        <section class="breadcrumb">
            <ul>
                <li><a href="#">Ana Səhifə</a></li>
                <li class="current-page"><div class="truncate">Şirkət Paneli</div></li>
            </ul>
        </section>

        <!-- Welcome Section -->
        <section class="welcome-section">
            <h1>Xoş gəlmisiniz, <span id="welcomeCompanyName">Kapital Bank</span>!</h1>
            <p>Şirkət panelinizə xoş gəlmisiniz. Burada müştəri rəylərini idarə edə və şirkətinizin reputasiyasını yaxşılaşdıra bilərsiniz.</p>
            <a href="#" class="logout-btn" id="logoutBtnMain">Çıxış</a>
        </section>

        <section class="filter-sort">
            <h1>Şirkətlər</h1>
            <article class="sort">
                <span>Sıralama</span>
                <select class="form-select form-select-sm" id="sortBy">
                    <option value="lastReviewed">Son rəylənən</option>
                    <option value="mostReviewed">Ən çox rəylənən</option>
                    <option value="mostViewed">Ən çox baxılan</option>
                    <option value="companyRating">Ən yüksək reytinq</option>
                </select>
            </article>
        </section>
        <div class="row">
            <aside class="col-md-3">
                <article class="categories">
                    <h5>Kateqoriyalar</h5>
                    <ul>
                        <li class="result active" data-category="all">Hamısı</li>
                        <li class="result" data-category="bank">Banklar</li>
                        <li class="result" data-category="telekom">Telekom</li>
                        <li class="result" data-category="yemek">Yemək Çatdırılması</li>
                        <li class="result" data-category="nəqliyyat">Nəqliyyat</li>
                        <li class="result" data-category="kommunal">Kommunal</li>
                        <li class="result" data-category="avtomobil">Avtomobil</li>
                        <li class="result" data-category="ticarət">Ticarət</li>
                        <li class="result" data-category="təhsil">Təhsil</li>
                        <li class="result" data-category="it">IT Xidmətləri</li>
                    </ul>
                </article>
            </aside>
            <section class="col-md-9">
                <article class="company-list" id="companyList">
                    <!-- Kapital Bank -->
                    <a href="#" class="company-wrapper" data-category="bank">
                        <section class="company">
                            <div class="company-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/4/47/Kapital_Bank_logo.svg" alt="Kapital Bank">
                            </div>
                            <div class="company-info">
                                <div class="company-name"><h5>Kapital Bank</h5></div>
                                <div class="star-rating__result">
                                    <div class="stars" data-rating="4.2">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star empty"></i>
                                    </div>
                                    <span>4.2</span>
                                </div>
                                <p class="company-title">Bank</p>
                                <p class="company-stats">Şikayətlər: 189 | Cavablar: 156</p>
                            </div>
                        </section>
                    </a>

                    <!-- Pasha Bank -->
                    <a href="#" class="company-wrapper" data-category="bank">
                        <section class="company">
                            <div class="company-logo">
                                <img src="https://www.pashabank.az/assets/images/logo.svg" alt="Pasha Bank">
                            </div>
                            <div class="company-info">
                                <div class="company-name"><h5>Pasha Bank</h5></div>
                                <div class="star-rating__result">
                                    <div class="stars" data-rating="3.8">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star empty"></i>
                                    </div>
                                    <span>3.8</span>
                                </div>
                                <p class="company-title">Bank</p>
                                <p class="company-stats">Şikayətlər: 65 | Cavablar: 52</p>
                            </div>
                        </section>
                    </a>

                    <!-- BirBank -->
                    <a href="#" class="company-wrapper" data-category="bank">
                        <section class="company">
                            <div class="company-logo">
                                <img src="https://www.kapitalbank.az/assets/images/birbank-logo.svg" alt="BirBank">
                            </div>
                            <div class="company-info">
                                <div class="company-name"><h5>BirBank</h5></div>
                                <div class="star-rating__result">
                                    <div class="stars" data-rating="4.5">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                    </div>
                                    <span>4.5</span>
                                </div>
                                <p class="company-title">Bank</p>
                                <p class="company-stats">Şikayətlər: 32 | Cavablar: 30</p>
                            </div>
                        </section>
                    </a>

                    <!-- Azercell -->
                    <a href="#" class="company-wrapper" data-category="telekom">
                        <section class="company">
                            <div class="company-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/8/8a/Azercell_logo.svg/200px-Azercell_logo.svg.png" alt="Azercell">
                            </div>
                            <div class="company-info">
                                <div class="company-name"><h5>Azercell</h5></div>
                                <div class="star-rating__result">
                                    <div class="stars" data-rating="3.2">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star empty"></i>
                                        <i class="far fa-star star empty"></i>
                                    </div>
                                    <span>3.2</span>
                                </div>
                                <p class="company-title">Telekom</p>
                                <p class="company-stats">Şikayətlər: 247 | Cavablar: 198</p>
                            </div>
                        </section>
                    </a>

                    <!-- Bolt Food -->
                    <a href="#" class="company-wrapper" data-category="yemek">
                        <section class="company">
                            <div class="company-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/1c/Bolt_logo.svg/200px-Bolt_logo.svg.png" alt="Bolt Food">
                            </div>
                            <div class="company-info">
                                <div class="company-name"><h5>Bolt Food</h5></div>
                                <div class="star-rating__result">
                                    <div class="stars" data-rating="4.1">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star empty"></i>
                                    </div>
                                    <span>4.1</span>
                                </div>
                                <p class="company-title">Yemək Çatdırılması</p>
                                <p class="company-stats">Şikayətlər: 156 | Cavablar: 134</p>
                            </div>
                        </section>
                    </a>

                    <!-- Wolt -->
                    <a href="#" class="company-wrapper" data-category="yemek">
                        <section class="company">
                            <div class="company-logo">
                                <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/3/3e/Wolt_logo.svg/200px-Wolt_logo.svg.png" alt="Wolt">
                            </div>
                            <div class="company-info">
                                <div class="company-name"><h5>Wolt</h5></div>
                                <div class="star-rating__result">
                                    <div class="stars" data-rating="4.3">
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="fas fa-star star"></i>
                                        <i class="far fa-star star empty"></i>
                                    </div>
                                    <span>4.3</span>
                                </div>
                                <p class="company-title">Yemək Çatdırılması</p>
                                <p class="company-stats">Şikayətlər: 43 | Cavablar: 41</p>
                            </div>
                        </section>
                    </a>
                </article>
                <section class="pagination">
                    <div class="pagination-wrapper">
                        <div class="page-numbers">
                            <a class="page active"><span>1</span></a>
                            <a class="page" href="#"><span>2</span></a>
                            <a class="page" href="#"><span>3</span></a>
                        </div>
                        <a class="page" href="#"><span>&raquo;</span></a>
                    </div>
                </section>
                <section class="companies-brandss">
                    <h3>Şirkətinizi əlavə edin</h3>
                    <p>Şirkətinizi qeydiyyatdan keçirin və müştərilərdən etibarlı rəylər toplamağa başlayın!</p>
                    <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#addCompanyModal">Şirkət əlavə et</button>
                </section>
            </section>
        </div>
    </div>
    <!-- Modal -->
    <div class="modal fade" id="addCompanyModal" tabindex="-1" aria-labelledby="addCompanyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="addCompanyModalLabel">Şirkət əlavə et</h4>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addCompanyForm">
                        <div class="mb-3">
                            <label for="firstname" class="form-label">Ad*</label>
                            <input type="text" class="company_sign_up_pop_up_input" id="firstname" placeholder="Ad" maxlength="50" required>
                        </div>
                        <div class="mb-3">
                            <label for="lastname" class="form-label">Soyad*</label>
                            <input type="text" class="company_sign_up_pop_up_input" id="lastname" placeholder="Soyad" maxlength="50" required>
                        </div>
                        <div class="mb-3">
                            <label for="company-name" class="form-label">Şirkətin adı*</label>
                            <input type="text" class="company_sign_up_pop_up_input" id="company-name" placeholder="Şirkətin | Brendin adı" maxlength="255" required>
                        </div>
                        <div class="mb-3">
                            <label for="phone" class="form-label">Əlaqə nömrəsi*</label>
                            <div style="display: flex; gap: 8px;">
                                <input type="text" value="+994" disabled style="width: 80px; padding: 12px; border: 1px solid #ddd; border-radius: 8px; background: #f8f9fa;">
                                <input type="text" class="company_sign_up_pop_up_input" id="phone" placeholder="*********" maxlength="15" required style="flex: 1;">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="email" class="form-label">Email*</label>
                            <input type="email" class="company_sign_up_pop_up_input" id="email" placeholder="<EMAIL>" maxlength="255" required>
                        </div>
                        <button type="submit" class="company_sign_up_pop_up_submit">Göndər</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</main>
<footer id="footer">
    <div class="container">
        <div class="footer_menu_wrapper">
            <div class="footer_menu">
                <div class="ft_links">
                    <a href="#" class="ft_link">Şirkətlər</a>
                    <a href="#" class="ft_link">Rəylər</a>
                    <a href="#" class="ft_link">Şikayətlər</a>
                    <a href="#" class="ft_link">Daxil ol</a>
                    <a href="#" class="ft_link">Şirkətlər üçün</a>
                </div>
                <div class="ft_links">
                    <a href="#" class="ft_link">Haqqımızda</a>
                    <a href="#" class="ft_link">Bloqlar</a>
                    <a href="#" class="ft_link">Tez-tez verilən suallar</a>
                    <a href="#" class="ft_link">Qaydalar</a>
                    <a href="#" class="ft_link">Məxfilik siyasəti</a>
                </div>
                <div class="ft_links">
                    <a href="tel:MRP LLC VOEN: 1308583041" class="ft_link">MRP LLC VOEN: 1308583041</a>
                    <a href="mailto:<EMAIL>" class="ft_link"><EMAIL></a>
                </div>
            </div>
            <div class="social_links">
                <a target="_blank" href="#"><i class="fab fa-facebook-f"></i></a>
                <a target="_blank" href="#"><i class="fab fa-instagram"></i></a>
                <a target="_blank" href="#"><i class="fab fa-linkedin-in"></i></a>
                <a target="_blank" href="#"><i class="fab fa-telegram-plane"></i></a>
            </div>
        </div>
    </div>
    <div class="footer_bottom">
        <div class="container">
            <div class="f_wrapper">
                <div class="f_logo">
                    <img src="https://cdn.jsdelivr.net/gh/grumble-org/grumble-repo@latest/img/grumble-logo.svg" alt="grumble.az">
                </div>
                <p class="f_copyright"><script>document.write(new Date().getFullYear());</script> © grumble.az</p>
            </div>
        </div>
    </div>
</footer>
<script src="https://cdn.jsdelivr.net/npm/jquery@3.7.1/dist/jquery.min.js" integrity="sha256-3gJwYp4gk1bZQzQ0jrISFRCGDpa2BkLomPvKg3r+7E0=" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-qQ2oNcamzatoorktrENcGukdxbYlR5c+3F4iAfDdc/K1Ji/7luWGINuD/R7/UZ5E" crossorigin="anonymous"></script>

<script>
document.addEventListener('DOMContentLoaded', function () {
    // Simulate logged in company data
    const loggedInCompany = {
        name: 'Kapital Bank',
        email: '<EMAIL>',
        category: 'Bank'
    };

    // Update company name displays
    document.getElementById('companyNameDisplay').textContent = loggedInCompany.name;
    document.getElementById('welcomeCompanyName').textContent = loggedInCompany.name;

    // User profile dropdown toggle
    const userProfileBtn = document.getElementById('userProfileBtn');
    const userDropdown = document.getElementById('userDropdown');

    if (userProfileBtn && userDropdown) {
        userProfileBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.style.display = userDropdown.style.display === 'none' ? 'block' : 'none';
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function() {
            userDropdown.style.display = 'none';
        });
    }

    // Logout functionality
    const logoutBtns = [document.getElementById('logoutBtn'), document.getElementById('logoutBtnMain')];
    logoutBtns.forEach(btn => {
        if (btn) {
            btn.addEventListener('click', function(e) {
                e.preventDefault();
                if (confirm('Çıxış etmək istədiyinizə əminsiniz?')) {
                    alert('Çıxış edildi. Giriş səhifəsinə yönləndirilirsiniz...');
                    // In a real app, this would redirect to login page
                    console.log('User logged out');
                }
            });
        }
    });

    // Category filtering
    const categoryItems = document.querySelectorAll('.categories .result');
    const companyWrappers = document.querySelectorAll('.company-wrapper');

    categoryItems.forEach(item => {
        item.addEventListener('click', function() {
            // Remove active class from all categories
            categoryItems.forEach(cat => cat.classList.remove('active'));
            // Add active class to clicked category
            this.classList.add('active');

            const selectedCategory = this.getAttribute('data-category');

            // Filter companies
            companyWrappers.forEach(wrapper => {
                if (selectedCategory === 'all' || wrapper.getAttribute('data-category') === selectedCategory) {
                    wrapper.style.display = 'block';
                } else {
                    wrapper.style.display = 'none';
                }
            });
        });
    });

    // Sorting functionality
    const sortSelect = document.getElementById('sortBy');
    if (sortSelect) {
        sortSelect.addEventListener('change', function() {
            const sortBy = this.value;
            const companyList = document.getElementById('companyList');
            const companies = Array.from(companyList.children);

            companies.sort((a, b) => {
                switch(sortBy) {
                    case 'companyRating':
                        const ratingA = parseFloat(a.querySelector('.star-rating__result span').textContent);
                        const ratingB = parseFloat(b.querySelector('.star-rating__result span').textContent);
                        return ratingB - ratingA;
                    case 'mostReviewed':
                        const reviewsA = parseInt(a.querySelector('.company-stats').textContent.match(/\d+/)[0]);
                        const reviewsB = parseInt(b.querySelector('.company-stats').textContent.match(/\d+/)[0]);
                        return reviewsB - reviewsA;
                    case 'lastReviewed':
                    case 'mostViewed':
                    default:
                        // For demo purposes, sort alphabetically
                        const nameA = a.querySelector('.company-name h5').textContent;
                        const nameB = b.querySelector('.company-name h5').textContent;
                        return nameA.localeCompare(nameB);
                }
            });

            // Re-append sorted companies
            companies.forEach(company => companyList.appendChild(company));
        });
    }

    // Search functionality
    const searchInput = document.getElementById('search-input');
    const searchForm = document.getElementById('search_form');

    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch();
        });
    }

    if (searchInput) {
        searchInput.addEventListener('input', function() {
            performSearch();
        });
    }

    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase().trim();

        companyWrappers.forEach(wrapper => {
            const companyName = wrapper.querySelector('.company-name h5').textContent.toLowerCase();
            const companyCategory = wrapper.querySelector('.company-title').textContent.toLowerCase();

            if (searchTerm === '' || companyName.includes(searchTerm) || companyCategory.includes(searchTerm)) {
                wrapper.style.display = 'block';
            } else {
                wrapper.style.display = 'none';
            }
        });
    }

    // Add Company Form
    const addCompanyForm = document.getElementById('addCompanyForm');
    if (addCompanyForm) {
        addCompanyForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = {
                firstName: document.getElementById('firstname').value,
                lastName: document.getElementById('lastname').value,
                companyName: document.getElementById('company-name').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value
            };

            // Validate form
            if (!formData.firstName || !formData.lastName || !formData.companyName || !formData.phone || !formData.email) {
                alert('Zəhmət olmasa bütün sahələri doldurun.');
                return;
            }

            // Email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(formData.email)) {
                alert('Zəhmət olmasa düzgün email daxil edin.');
                return;
            }

            // Phone validation
            const phoneRegex = /^\d{9}$/;
            if (!phoneRegex.test(formData.phone)) {
                alert('Zəhmət olmasa düzgün telefon nömrəsi daxil edin (9 rəqəm).');
                return;
            }

            // Simulate form submission
            console.log('Company registration data:', formData);
            alert(`Təşəkkür edirik, ${formData.firstName}! ${formData.companyName} şirkəti üçün müraciətiniz qəbul edildi. Tezliklə sizinlə əlaqə saxlayacağıq.`);

            // Close modal and reset form
            const modal = bootstrap.Modal.getInstance(document.getElementById('addCompanyModal'));
            if (modal) {
                modal.hide();
            }
            addCompanyForm.reset();
        });
    }

    // Dynamic star rating display
    function updateStarRating(starsContainer, rating) {
        const stars = starsContainer.querySelectorAll('.star');
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 !== 0;

        stars.forEach((star, index) => {
            if (index < fullStars) {
                star.className = 'fas fa-star star';
            } else if (index === fullStars && hasHalfStar) {
                star.className = 'fas fa-star-half-alt star';
            } else {
                star.className = 'far fa-star star empty';
            }
        });
    }

    // Initialize star ratings
    document.querySelectorAll('.stars[data-rating]').forEach(starsContainer => {
        const rating = parseFloat(starsContainer.getAttribute('data-rating'));
        updateStarRating(starsContainer, rating);
    });

    // Write review button functionality
    const writeReviewBtn = document.getElementById('write-review-btn');
    if (writeReviewBtn) {
        writeReviewBtn.addEventListener('click', function() {
            alert('Rəy yazma səhifəsinə yönləndirilirsiniz...');
            console.log('Navigate to write review page');
        });
    }

    // Mobile menu toggle (basic implementation)
    const hamMenu = document.querySelector('.ham-menu');
    const mobileMenu = document.querySelector('.mobile-menu-wrapper');

    if (hamMenu && mobileMenu) {
        hamMenu.addEventListener('click', function() {
            mobileMenu.style.display = mobileMenu.style.display === 'block' ? 'none' : 'block';
        });
    }

    // Language selection
    const langSelection = document.querySelector('.language_selection');
    if (langSelection) {
        const langTitle = langSelection.querySelector('.title');
        const langBody = langSelection.querySelector('.lang_body');
        const langLinks = langBody.querySelectorAll('a');

        langLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const selectedLang = this.textContent;
                langTitle.textContent = selectedLang;
                console.log('Language changed to:', selectedLang);
            });
        });
    }

    // Pagination functionality (basic)
    const pageLinks = document.querySelectorAll('.page-numbers .page');
    pageLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all pages
            pageLinks.forEach(p => p.classList.remove('active'));
            // Add active class to clicked page
            this.classList.add('active');

            console.log('Page changed to:', this.textContent);
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth'
                });
            }
        });
    });
});

// Utility function to get CSRF token (for future AJAX requests)
function getCsrfToken() {
    const token = document.querySelector('[name=csrfmiddlewaretoken]');
    return token ? token.value : '';
}

// Simulate login state check
function checkLoginState() {
    // In a real application, this would check with the server
    return {
        isLoggedIn: true,
        userType: 'company',
        companyData: {
            name: 'Kapital Bank',
            email: '<EMAIL>',
            category: 'Bank'
        }
    };
}

// Initialize page based on login state
const loginState = checkLoginState();
if (loginState.isLoggedIn && loginState.userType === 'company') {
    console.log('Company logged in:', loginState.companyData.name);
} else {
    console.log('User not logged in or not a company');
}
</script>

</body>
</html>